# Three.js Canvas 尺寸更新问题修复

## 问题描述
在全屏状态切换时，`resizeFun` 函数先于 DOM 更新完成被触发，导致 canvas 获取到的是旧的容器尺寸而不是新的尺寸。

## 问题原因
1. YSXK-ScaleDiv 组件的 props 变化触发 `setScale()`
2. `setScale()` 改变容器样式，触发 window resize 事件
3. `resizeFun` 在 DOM 更新完成前被调用，获取到旧的尺寸

## 解决方案

### 1. 在 Three.js 组件中使用 nextTick
- 在 `resizeFun` 中使用 `nextTick` 确保 DOM 更新完成后再获取尺寸
- 添加 `handleContainerResize` 方法，使用 `setTimeout` 进一步延迟执行

### 2. 在 YSXK-ScaleDiv 组件中优化时序
- 在 props 变化的 watch 中使用 `nextTick`
- 在 `onMounted` 中使用 `nextTick`

### 3. 在父组件中主动触发 resize 事件
- 在全屏状态变化时，延迟触发 resize 事件
- 确保所有组件都能正确响应尺寸变化

## 修改的文件
1. `src/views/dashboard/Analysis/components/threeJs/index.vue`
2. `src/views/dashboard/Analysis/components/YSXK-ScaleDiv.vue`
3. `src/views/dashboard/Analysis/index.vue`

## 测试步骤
1. 启动应用
2. 进入 Analysis 页面
3. 点击全屏按钮
4. 观察控制台输出的容器尺寸是否正确
5. 测试 ESC 键退出全屏
6. 测试侧边栏折叠/展开时的尺寸变化

## 预期结果
- 全屏时：容器尺寸应该是 1920x1080
- 非全屏时：容器尺寸应该根据侧边栏状态动态变化
- 不再出现 "先触发 resizeFun，导致 canvas 的大小没有基于父元素的大小" 的问题
