
// 导入three.js
import * as THREE from 'three'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
// import { watch, type Ref } from 'vue'

// 全局状态管理
// import { universalFoundationPosition, levelPosition, selectLevel, isLabelShow } from './PublicData'

// 调试工具
// import { GUI } from 'three/addons/libs/lil-gui.module.min.js'

// const gui = new GUI( { width: 280 } )

export interface ControlsConfigData {
  target: THREE.Vector3
  minDistance: number
  maxDistance: number
}

/**
 * 相机轨道控制器 阻尼相机移动 CameraControls
 */
export default (camera: THREE.PerspectiveCamera, canvas: HTMLCanvasElement | undefined, controlsConfigData: ControlsConfigData): OrbitControls => {
  // 相机阻尼移动 相机 画布
  const controls = new OrbitControls(camera, canvas)
  controls.enableDamping = true // 打开阻尼
  controls.dampingFactor = 0.0669 // 阻尼
  controls.zoomSpeed = 2.669 // 缩放速度
  controls.autoRotate = true // 自动旋转
  controls.autoRotateSpeed = .4 // 自动旋转速度
  controls.screenSpacePanning = false // 平移方式
  // 相机视角限制
  controls.maxPolarAngle = Math.PI * .3

  controls.minDistance = controlsConfigData.minDistance // 滚轮最小缩放 向内移动最小值
  controls.maxDistance = controlsConfigData.maxDistance // 滚轮最大缩放 向外移动最大值

  controls.target.set(
    controlsConfigData.target.x,
    controlsConfigData.target.y,
    controlsConfigData.target.z
  )

  // gui.add(controls.target, 'x').min(-50).max(50).step(.1).name('controls.target.x')
  // gui.add(controls.target, 'y').min(-50).max(50).step(.1).name('controls.target.y')
  // gui.add(controls.target, 'z').min(-50).max(50).step(.1).name('controls.target.z')

  return controls
}